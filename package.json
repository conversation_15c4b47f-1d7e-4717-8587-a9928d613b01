{"name": "trustay-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-react": "^11.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^15.4.2", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/eslintrc": "^3", "@redux-devtools/extension": "^3.3.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "lefthook": "^1.12.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5"}}