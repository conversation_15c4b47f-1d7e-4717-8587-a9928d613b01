"use client"

import React, { useRef, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  error?: boolean
}

export function RichTextEditor({
  value = '',
  onChange,
  placeholder = 'Nhập mô tả...',
  className,
  disabled = false,
  error = false
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [editor, setEditor] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    let editorInstance: any = null
    let isMounted = true

    const initEditor = async () => {
      try {
        setIsLoading(true)
        setHasError(false)

        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return
        }

        // Dynamic import CKEditor to avoid SSR issues
        const CKEditor = await import('@ckeditor/ckeditor5-build-classic')
        const ClassicEditor = CKEditor.default

        if (editorRef.current && isMounted) {
          editorInstance = await ClassicEditor.create(editorRef.current, {
            placeholder,
            toolbar: [
              'heading',
              '|',
              'bold',
              'italic',
              '|',
              'bulletedList',
              'numberedList',
              '|',
              'blockQuote',
              '|',
              'undo',
              'redo'
            ],
            heading: {
              options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' }
              ]
            }
          })

          if (!isMounted) {
            editorInstance.destroy()
            return
          }

          // Set initial value
          if (value) {
            editorInstance.setData(value)
          }

          // Listen for changes with debounce
          let timeoutId: NodeJS.Timeout
          editorInstance.model.document.on('change:data', () => {
            clearTimeout(timeoutId)
            timeoutId = setTimeout(() => {
              if (isMounted) {
                const data = editorInstance.getData()
                onChange?.(data)
              }
            }, 300) // Debounce 300ms
          })

          // Handle disabled state
          if (disabled) {
            editorInstance.enableReadOnlyMode('disabled')
          }

          setEditor(editorInstance)
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Error initializing CKEditor:', error)
        if (isMounted) {
          setHasError(true)
          setIsLoading(false)
        }
      }
    }

    // Add a small delay to ensure DOM is ready
    const timer = setTimeout(initEditor, 100)

    return () => {
      isMounted = false
      clearTimeout(timer)
      if (editorInstance) {
        editorInstance.destroy().catch(console.error)
      }
    }
  }, [])

  // Update editor when value changes externally
  useEffect(() => {
    if (editor && value !== editor.getData()) {
      editor.setData(value)
    }
  }, [value, editor])

  // Update disabled state
  useEffect(() => {
    if (editor) {
      if (disabled) {
        editor.enableReadOnlyMode('disabled')
      } else {
        editor.disableReadOnlyMode('disabled')
      }
    }
  }, [disabled, editor])

  if (isLoading) {
    return (
      <div className={cn(
        "min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
        "flex items-center justify-center text-muted-foreground",
        error && "border-destructive",
        className
      )}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Đang tải editor...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "rich-text-editor",
      error && "border-destructive",
      className
    )}>
      <div 
        ref={editorRef}
        className={cn(
          "min-h-[200px] w-full",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      />
      <style jsx global>{`
        .rich-text-editor .ck-editor__editable {
          min-height: 200px;
          border-radius: 0.375rem;
          border: 1px solid hsl(var(--border));
          background: hsl(var(--background));
        }
        
        .rich-text-editor .ck-editor__editable:focus {
          border-color: hsl(var(--ring));
          box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }
        
        .rich-text-editor .ck-toolbar {
          border-radius: 0.375rem 0.375rem 0 0;
          border: 1px solid hsl(var(--border));
          border-bottom: none;
          background: hsl(var(--muted));
        }
        
        .rich-text-editor .ck-editor__main {
          border-radius: 0 0 0.375rem 0.375rem;
        }
        
        ${error ? `
          .rich-text-editor .ck-editor__editable {
            border-color: hsl(var(--destructive));
          }
          .rich-text-editor .ck-toolbar {
            border-color: hsl(var(--destructive));
          }
        ` : ''}
      `}</style>
    </div>
  )
}
