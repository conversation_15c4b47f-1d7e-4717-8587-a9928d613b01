"use client"

import React, { useState } from 'react'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import { cn } from '@/lib/utils'

// Import CKEditor CSS
import 'ckeditor5/ckeditor5.css'

interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  error?: boolean
}

export function RichTextEditor({
  value = '',
  onChange,
  placeholder = 'Nhập mô tả...',
  className,
  disabled = false,
  error = false
}: RichTextEditorProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [ClassicEditor, setClassicEditor] = useState<any>(null)
  const [hasError, setHasError] = useState(false)

  React.useEffect(() => {
    const loadEditor = async () => {
      try {
        const {
          ClassicEditor: ClassicEditorBuild,
          Bold,
          Italic,
          Essentials,
          Paragraph,
          Heading,
          List,
          BlockQuote,
          Undo
        } = await import('ckeditor5')

        // Configure the editor
        ClassicEditorBuild.builtinPlugins = [
          Essentials,
          Bold,
          Italic,
          Paragraph,
          Heading,
          List,
          BlockQuote,
          Undo
        ]

        ClassicEditorBuild.defaultConfig = {
          licenseKey: 'GPL',
          toolbar: {
            items: [
              'heading',
              '|',
              'bold',
              'italic',
              '|',
              'bulletedList',
              'numberedList',
              '|',
              'blockQuote',
              '|',
              'undo',
              'redo'
            ]
          },
          placeholder
        }

        setClassicEditor(() => ClassicEditorBuild)
        setIsLoading(false)
      } catch (error) {
        console.error('Error loading CKEditor:', error)
        setHasError(true)
        setIsLoading(false)
      }
    }

    loadEditor()
  }, [placeholder])

  // Fallback to textarea if CKEditor fails to load
  if (isLoading || !ClassicEditor || hasError) {
    if (isLoading) {
      return (
        <div className={cn(
          "min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
          "flex items-center justify-center text-muted-foreground",
          error && "border-destructive",
          className
        )}>
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Đang tải editor...</span>
          </div>
        </div>
      )
    }

    // Fallback to simple textarea
    return (
      <div className={cn("space-y-2", className)}>
        <textarea
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
            "placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
            "disabled:cursor-not-allowed disabled:opacity-50",
            error && "border-destructive",
          )}
          rows={8}
        />
        {hasError && (
          <p className="text-xs text-muted-foreground">
            Rich text editor không khả dụng, sử dụng text editor đơn giản
          </p>
        )}
      </div>
    )
  }

  return (
    <div className={cn(
      "rich-text-editor",
      error && "border-destructive",
      className
    )}>
      <CKEditor
        editor={ClassicEditor}
        data={value}
        config={{
          licenseKey: 'GPL',
          placeholder,
          toolbar: [
            'heading',
            '|',
            'bold',
            'italic',
            '|',
            'bulletedList',
            'numberedList',
            '|',
            'blockQuote',
            '|',
            'undo',
            'redo'
          ]
        }}
        onChange={(event: any, editor: any) => {
          const data = editor.getData()
          onChange?.(data)
        }}
        disabled={disabled}
      />
      <style jsx global>{`
        .rich-text-editor .ck-editor__editable {
          min-height: 200px;
          border-radius: 0.375rem;
          border: 1px solid hsl(var(--border));
          background: hsl(var(--background));
        }

        .rich-text-editor .ck-editor__editable:focus {
          border-color: hsl(var(--ring));
          box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .rich-text-editor .ck-toolbar {
          border-radius: 0.375rem 0.375rem 0 0;
          border: 1px solid hsl(var(--border));
          border-bottom: none;
          background: hsl(var(--muted));
        }

        .rich-text-editor .ck-editor__main {
          border-radius: 0 0 0.375rem 0.375rem;
        }

        ${error ? `
          .rich-text-editor .ck-editor__editable {
            border-color: hsl(var(--destructive));
          }
          .rich-text-editor .ck-toolbar {
            border-color: hsl(var(--destructive));
          }
        ` : ''}
      `}</style>
    </div>
  )
}
