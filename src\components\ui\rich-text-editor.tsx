"use client"

import React, { useRef, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  error?: boolean
}

export function RichTextEditor({
  value = '',
  onChange,
  placeholder = 'Nhập mô tả...',
  className,
  disabled = false,
  error = false
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [editor, setEditor] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let editorInstance: any = null

    const initEditor = async () => {
      try {
        // Dynamic import CKEditor to avoid SSR issues
        const { default: ClassicEditor } = await import('@ckeditor/ckeditor5-build-classic')
        
        if (editorRef.current) {
          editorInstance = await ClassicEditor.create(editorRef.current, {
            placeholder,
            toolbar: [
              'heading',
              '|',
              'bold',
              'italic',
              'underline',
              '|',
              'bulletedList',
              'numberedList',
              '|',
              'outdent',
              'indent',
              '|',
              'blockQuote',
              'insertTable',
              '|',
              'undo',
              'redo'
            ],
            heading: {
              options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
              ]
            },
            table: {
              contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
            }
          })

          // Set initial value
          if (value) {
            editorInstance.setData(value)
          }

          // Listen for changes
          editorInstance.model.document.on('change:data', () => {
            const data = editorInstance.getData()
            onChange?.(data)
          })

          // Handle disabled state
          if (disabled) {
            editorInstance.enableReadOnlyMode('disabled')
          }

          setEditor(editorInstance)
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Error initializing CKEditor:', error)
        setIsLoading(false)
      }
    }

    initEditor()

    return () => {
      if (editorInstance) {
        editorInstance.destroy()
      }
    }
  }, [])

  // Update editor when value changes externally
  useEffect(() => {
    if (editor && value !== editor.getData()) {
      editor.setData(value)
    }
  }, [value, editor])

  // Update disabled state
  useEffect(() => {
    if (editor) {
      if (disabled) {
        editor.enableReadOnlyMode('disabled')
      } else {
        editor.disableReadOnlyMode('disabled')
      }
    }
  }, [disabled, editor])

  if (isLoading) {
    return (
      <div className={cn(
        "min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
        "flex items-center justify-center text-muted-foreground",
        error && "border-destructive",
        className
      )}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Đang tải editor...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "rich-text-editor",
      error && "border-destructive",
      className
    )}>
      <div 
        ref={editorRef}
        className={cn(
          "min-h-[200px] w-full",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      />
      <style jsx global>{`
        .rich-text-editor .ck-editor__editable {
          min-height: 200px;
          border-radius: 0.375rem;
          border: 1px solid hsl(var(--border));
          background: hsl(var(--background));
        }
        
        .rich-text-editor .ck-editor__editable:focus {
          border-color: hsl(var(--ring));
          box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }
        
        .rich-text-editor .ck-toolbar {
          border-radius: 0.375rem 0.375rem 0 0;
          border: 1px solid hsl(var(--border));
          border-bottom: none;
          background: hsl(var(--muted));
        }
        
        .rich-text-editor .ck-editor__main {
          border-radius: 0 0 0.375rem 0.375rem;
        }
        
        ${error ? `
          .rich-text-editor .ck-editor__editable {
            border-color: hsl(var(--destructive));
          }
          .rich-text-editor .ck-toolbar {
            border-color: hsl(var(--destructive));
          }
        ` : ''}
      `}</style>
    </div>
  )
}
